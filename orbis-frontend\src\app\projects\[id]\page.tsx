'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Modal } from '@/components/ui/Modal'
import { Input } from '@/components/ui/Input'
import ProtectedRoute from '@/components/ProtectedRoute'
import ModernSidebar from '@/components/ModernSidebar'
import ModernHeader from '@/components/ModernHeader'
import Link from 'next/link'
import { api } from '@/lib/api'

export default function ProjectDetails() {
  const { user, signOut } = useAuth()
  const params = useParams()
  // Keep router for potential future use
  const router = useRouter()
  const [project, setProject] = useState<Record<string, any> | null>(null)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchProject = async () => {
      try {
        setLoading(true)
        const projectId = parseInt(params.id as string)
        const data = await api.getProject(projectId)
        setProject(data)
        setError(null)
      } catch (err) {
        console.error('Error fetching project:', err)
        setError('Erreur lors du chargement des détails du projet')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchProject()
    }
  }, [params.id])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="text-center">
          <div className="text-red-600 text-lg mb-4">{error}</div>
          <Button onClick={() => window.location.reload()}>
            Réessayer
          </Button>
        </div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-gray-600">Projet non trouvé</div>
      </div>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'En cours': return 'bg-blue-100 text-blue-800'
      case 'Terminé': return 'bg-green-100 text-green-800'
      case 'En attente': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Fonction de déconnexion
  const handleLogout = () => {
    signOut()
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <div className="flex">
          <ModernSidebar user={user ? {
            name: `${user.first_name} ${user.last_name}`,
            email: user.email
          } : undefined} />

          <div className="flex-1 lg:ml-0">
            <ModernHeader
              title={project ? project.name : "Détails du projet"}
              subtitle={project ? project.description : "Chargement des détails du projet..."}
              user={user ? {
                name: `${user.first_name} ${user.last_name}`,
                email: user.email
              } : undefined}
              onLogout={handleLogout}
            />

            <main className="p-6">
              <div className="space-y-6">
                {/* Header */}
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center gap-4 mb-2">
                      <h1 className="text-3xl font-bold text-gray-900">{project.name}</h1>
                      <span className={`px-3 py-1 text-sm rounded-full ${getStatusColor(project.status)}`}>
                        {project.status}
                      </span>
                    </div>
                    <p className="text-gray-600">{project.description}</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" onClick={() => setIsEditModalOpen(true)}>
                      Modifier
                    </Button>
                    <Link href="/projects">
                      <Button variant="outline">Retour</Button>
                    </Link>
                  </div>
                </div>

      {/* Progress Bar */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Progression du projet</span>
          <span className="text-sm font-medium text-gray-700">{project.progress}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className="bg-blue-600 h-3 rounded-full transition-all duration-300"
            style={{ width: `${project.progress}%` }}
          ></div>
        </div>
      </Card>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Vue d\'ensemble' },
            { id: 'tasks', label: 'Tâches' },
            { id: 'team', label: 'Équipe' },
            { id: 'documents', label: 'Documents' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Project Info */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Informations du projet</h2>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Client:</span>
                <span className="font-medium">{project.client}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Chef de projet:</span>
                <span className="font-medium">{project.manager}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Localisation:</span>
                <span className="font-medium">{project.location}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Date de début:</span>
                <span className="font-medium">{new Date(project.startDate).toLocaleDateString('fr-FR')}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Date de fin prévue:</span>
                <span className="font-medium">{new Date(project.endDate).toLocaleDateString('fr-FR')}</span>
              </div>
            </div>
          </Card>

          {/* Budget */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Budget</h2>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Budget total:</span>
                <span className="text-xl font-bold text-green-600">
                  {project.budget.toLocaleString('fr-FR')} €
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Dépenses:</span>
                <span className="text-xl font-bold text-orange-600">
                  {project.spent.toLocaleString('fr-FR')} €
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Restant:</span>
                <span className="text-xl font-bold text-blue-600">
                  {(project.budget - project.spent).toLocaleString('fr-FR')} €
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-orange-500 h-2 rounded-full"
                  style={{ width: `${(project.spent / project.budget) * 100}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-600">
                {((project.spent / project.budget) * 100).toFixed(1)}% du budget utilisé
              </p>
            </div>
          </Card>
        </div>
      )}

      {activeTab === 'tasks' && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Tâches du projet</h2>
          <div className="space-y-4">
            {project.tasks.map((task: any) => (
              <div key={task.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-medium">{task.name}</h3>
                  <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(task.status)}`}>
                    {task.status}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex-1 mr-4">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${task.progress}%` }}
                      ></div>
                    </div>
                  </div>
                  <span className="text-sm text-gray-600">{task.progress}%</span>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {activeTab === 'team' && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Équipe du projet</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {project.employees.map((employee: any) => (
              <div key={employee.id} className="border rounded-lg p-4">
                <h3 className="font-medium text-gray-900">{employee.name}</h3>
                <p className="text-sm text-gray-600 mb-2">{employee.role}</p>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">Heures travaillées:</span>
                  <span className="font-medium text-blue-600">{employee.hours}h</span>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {activeTab === 'documents' && (
        <Card className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Documents du projet</h2>
            <Button className="bg-blue-600 hover:bg-blue-700">
              Ajouter Document
            </Button>
          </div>
          <div className="space-y-3">
            {project.documents.map((doc: any) => (
              <div key={doc.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <span className="text-blue-600 font-medium">📄</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">{doc.name}</h3>
                    <div className="flex items-center text-sm text-gray-500">
                      <span>{doc.type}</span>
                      <span className="mx-2">•</span>
                      <span>{doc.size}</span>
                      <span className="mx-2">•</span>
                      <span>{new Date(doc.date).toLocaleDateString('fr-FR')}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">Télécharger</Button>
                  <Button variant="outline" size="sm">Voir</Button>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

                {/* Edit Modal */}
                <Modal isOpen={isEditModalOpen} onClose={() => setIsEditModalOpen(false)}>
                  <div className="p-6">
                    <h2 className="text-xl font-semibold mb-4">Modifier le projet</h2>
                    <div className="space-y-4">
                      <Input label="Nom du projet" defaultValue={project.name} />
                      <Input label="Description" defaultValue={project.description} />
                      <Input label="Budget" type="number" defaultValue={project.budget} />
                      <div className="flex justify-end gap-2 mt-6">
                        <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                          Annuler
                        </Button>
                        <Button onClick={() => setIsEditModalOpen(false)}>
                          Enregistrer
                        </Button>
                      </div>
                    </div>
                  </div>
                </Modal>
              </div>
            </main>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}