'use client'

import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { BusinessDataService } from '@/lib/auth'
import ProtectedRoute from '@/components/ProtectedRoute'
import ModernSidebar from '@/components/ModernSidebar'
import ModernHeader from '@/components/ModernHeader'

import Link from 'next/link'

export default function Dashboard() {
  const { user, loading, signOut } = useAuth()
  const [isLoading, setIsLoading] = useState(true)

  const [recentProjects, setRecentProjects] = useState<any[]>([])

  const loadDashboardData = useCallback(async () => {
    try {
      setIsLoading(true)

      // Charger les projets récents
      const projects = await BusinessDataService.getProjects()
      setRecentProjects(projects.slice(0, 5)) // Prendre les 5 premiers

    } catch (error) {
      console.error('Erreur chargement dashboard:', error)
      // En cas d'erreur, on garde une liste vide
      setRecentProjects([])
    } finally {
      setIsLoading(false)
    }
  }, []) // Pas de dépendances car on utilise seulement des setters

  // Charger les données du dashboard
  useEffect(() => {
    if (user && !loading) {
      loadDashboardData()
    }
  }, [user, loading, loadDashboardData])

  // Fonction de déconnexion
  const handleLogout = () => {
    signOut()
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <div className="flex">
          <ModernSidebar user={user ? {
            name: `${user.first_name} ${user.last_name}`,
            email: user.email
          } : undefined} />

          <div className="flex-1 lg:ml-0">
            <ModernHeader
              title="Dashboard"
              subtitle={`Bienvenue, ${user?.first_name} ${user?.last_name} - Vue d'ensemble des projets et activités`}
              user={user ? {
                name: `${user.first_name} ${user.last_name}`,
                email: user.email
              } : undefined}
              onLogout={handleLogout}
            />

          <main className="p-6">
            <div className="space-y-8">
              {/* Welcome Section */}
              <div className="bg-gradient-to-r from-primary-600 to-cyan-600 rounded-2xl p-8 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold mb-2">Bienvenue sur ORBIS</h2>
                    <p className="text-primary-100 mb-4">Votre plateforme de gestion de projets BTP</p>
                    <div className="flex items-center space-x-4">
                      <button className="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg transition-all duration-200">
                        Nouveau Projet
                      </button>
                      <span className="text-primary-100 text-sm">
                        Dernière connexion: {new Date().toLocaleDateString('fr-FR')}
                      </span>
                    </div>
                  </div>
                  <div className="hidden md:block text-6xl opacity-20">
                    🏗️
                  </div>
                </div>
              </div>

              {/* Welcome Message */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Bienvenue sur ORBIS
                </h2>
                <p className="text-gray-600">
                  Tableau de bord principal pour la gestion de vos projets et équipes.
                </p>
              </div>

              {/* Charts and Recent Projects */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Modern Chart Card */}
                <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                  <div className="p-6 border-b border-gray-100">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">Évolution des Projets</h3>
                        <p className="text-sm text-gray-500 mt-1">Progression mensuelle</p>
                      </div>
                      <div className="p-2 bg-primary-100 rounded-lg">
                        <svg className="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                  <div className="p-6">
                    {isLoading ? (
                      <div className="h-64 flex items-center justify-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                      </div>
                    ) : (
                      <div className="h-64 bg-gradient-to-br from-primary-50 to-cyan-50 rounded-xl flex items-center justify-center">
                        <div className="text-center">
                          <div className="text-4xl mb-4">📊</div>
                          <p className="text-gray-600">Graphique interactif</p>
                          <p className="text-sm text-gray-500">Données en temps réel</p>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Dernière mise à jour: {new Date().toLocaleDateString('fr-FR')}</span>
                      <Link href="/reports" className="text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors">
                        Voir rapports →
                      </Link>
                    </div>
                  </div>
                </div>

                {/* Modern Recent Projects Card */}
                <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                  <div className="p-6 border-b border-gray-100">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">Projets Récents</h3>
                        <p className="text-sm text-gray-500 mt-1">Dernières activités</p>
                      </div>
                      <div className="p-2 bg-green-100 rounded-lg">
                        <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                      </div>
                    </div>
                  </div>
                  <div className="p-6">
                    {isLoading ? (
                      <div className="space-y-4">
                        {[1, 2, 3].map(i => (
                          <div key={i} className="animate-pulse flex items-center space-x-4">
                            <div className="rounded-full bg-gray-200 h-10 w-10"></div>
                            <div className="flex-1 space-y-2">
                              <div className="h-4 bg-gray-200 rounded"></div>
                              <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {recentProjects.length > 0 ? recentProjects.map((project: { id: number; name: string; status: string; progress: number; client: string; dueDate: string }) => (
                          <div key={project.id} className="p-4 rounded-xl bg-gray-50 hover:bg-gray-100 transition-all duration-200 border border-gray-100">
                            <div className="flex items-center space-x-4">
                              <div className="flex-shrink-0">
                                <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                                  project.status === 'En cours'
                                    ? 'bg-blue-100 text-blue-600'
                                    : project.status === 'Terminé'
                                    ? 'bg-green-100 text-green-600'
                                    : 'bg-yellow-100 text-yellow-600'
                                }`}>
                                  {project.status === 'En cours' ? '🚧' : project.status === 'Terminé' ? '✅' : '⏳'}
                                </div>
                              </div>
                              <div className="flex-1 min-w-0">
                                <Link href={`/projects/${project.id}`} className="text-sm font-semibold text-gray-900 hover:text-blue-600 block transition-colors">
                                  {project.name}
                                </Link>
                                <div className="flex items-center mt-1 text-xs text-gray-500 space-x-4">
                                  <span className="flex items-center">
                                    👤 {project.client}
                                  </span>
                                  <span className="flex items-center">
                                    📅 {project.dueDate}
                                  </span>
                                </div>
                                <div className="mt-2">
                                  <div className="flex items-center justify-between text-xs">
                                    <span className="text-gray-600">Progression</span>
                                    <span className="font-medium text-gray-900">{project.progress}%</span>
                                  </div>
                                  <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
                                    <div
                                      className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-500"
                                      style={{ width: `${project.progress}%` }}
                                    ></div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )) : (
                          <div className="text-center py-8">
                            <div className="text-4xl mb-4">📋</div>
                            <p className="text-gray-500">Aucun projet récent</p>
                            <p className="text-sm text-gray-400">Créez votre premier projet</p>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                  <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
                    <Link href="/projects" className="text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors">
                      Voir tous les projets →
                    </Link>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-6">Actions Rapides</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <button className="p-4 rounded-xl bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 transition-all duration-200 group">
                    <div className="text-center">
                      <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                      </div>
                      <span className="font-medium text-gray-900">Nouveau Projet</span>
                      <p className="text-xs text-gray-500 mt-1">Créer un projet</p>
                    </div>
                  </button>

                  <button className="p-4 rounded-xl bg-gradient-to-br from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200 transition-all duration-200 group">
                    <div className="text-center">
                      <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                      </div>
                      <span className="font-medium text-gray-900">Ajouter Employé</span>
                      <p className="text-xs text-gray-500 mt-1">Gérer l'équipe</p>
                    </div>
                  </button>

                  <button className="p-4 rounded-xl bg-gradient-to-br from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 transition-all duration-200 group">
                    <div className="text-center">
                      <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <span className="font-medium text-gray-900">Documents</span>
                      <p className="text-xs text-gray-500 mt-1">Gérer fichiers</p>
                    </div>
                  </button>

                  <button className="p-4 rounded-xl bg-gradient-to-br from-orange-50 to-orange-100 hover:from-orange-100 hover:to-orange-200 transition-all duration-200 group">
                    <div className="text-center">
                      <div className="w-12 h-12 bg-orange-500 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0-6l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2" />
                        </svg>
                      </div>
                      <span className="font-medium text-gray-900">Rapports</span>
                      <p className="text-xs text-gray-500 mt-1">Voir statistiques</p>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </main>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
