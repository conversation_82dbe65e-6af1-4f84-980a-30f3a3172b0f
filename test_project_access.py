#!/usr/bin/env python3
"""
Test d'accès aux projets pour diagnostiquer le problème de redirection
"""

import requests
import json

API_BASE = "http://localhost:8000"

def test_project_access():
    """Test l'accès aux projets avec l'utilisateur <EMAIL>"""
    print("🧪 Test d'accès aux projets")
    print("="*50)
    
    # 1. Connexion
    print("1. 🔐 Connexion...")
    login_response = requests.post(f"{API_BASE}/api/v1/fast-auth/login", json={
        "email": "<EMAIL>",
        "password": "orbis123!"
    })
    
    if login_response.status_code != 200:
        print(f"❌ Erreur de connexion: {login_response.status_code}")
        print(f"   Response: {login_response.text}")
        return False
    
    token_data = login_response.json()
    token = token_data['access_token']
    user = token_data['user']
    print(f"✅ Connexion réussie pour: {user['email']}")
    print(f"   Rôle: {user.get('role', 'N/A')}")
    print(f"   Super admin: {user.get('is_superuser', False)}")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 2. Test de vérification du token
    print("\n2. 🔍 Vérification du token...")
    verify_response = requests.get(f"{API_BASE}/api/v1/fast-auth/verify", headers=headers)
    print(f"   Status: {verify_response.status_code}")
    if verify_response.status_code == 200:
        print("✅ Token valide")
    else:
        print(f"❌ Token invalide: {verify_response.text}")
        return False
    
    # 3. Test de récupération des projets
    print("\n3. 📋 Récupération des projets...")
    projects_response = requests.get(f"{API_BASE}/api/v1/projects", headers=headers)
    print(f"   Status: {projects_response.status_code}")
    
    if projects_response.status_code == 200:
        projects = projects_response.json()
        print(f"✅ {len(projects)} projet(s) trouvé(s)")
        for i, project in enumerate(projects[:3]):  # Afficher les 3 premiers
            print(f"   - Projet {i+1}: {project.get('name', 'N/A')} (ID: {project.get('id', 'N/A')})")
        
        # 4. Test d'accès à un projet spécifique
        if projects:
            project_id = projects[0]['id']
            print(f"\n4. 🎯 Test d'accès au projet {project_id}...")
            project_response = requests.get(f"{API_BASE}/api/v1/projects/{project_id}", headers=headers)
            print(f"   Status: {project_response.status_code}")
            
            if project_response.status_code == 200:
                project = project_response.json()
                print(f"✅ Projet récupéré: {project.get('name', 'N/A')}")
                print(f"   Structure du projet:")
                print(f"   {json.dumps(project, indent=2, default=str)}")
                return True
            else:
                print(f"❌ Erreur d'accès au projet: {project_response.text}")
                return False
        else:
            print("\n4. ⚠️  Aucun projet disponible pour tester l'accès individuel")
            return True
            
    else:
        print(f"❌ Erreur de récupération des projets: {projects_response.text}")
        return False

if __name__ == "__main__":
    success = test_project_access()
    print(f"\n{'='*50}")
    if success:
        print("✅ Test réussi - L'accès aux projets fonctionne")
    else:
        print("❌ Test échoué - Problème d'accès aux projets")
