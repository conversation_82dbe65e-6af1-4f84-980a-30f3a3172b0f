# app/api/api_v1/endpoints/entreprises_tiers.py
"""
API endpoints pour les entreprises tierces (carnet d'adresses)
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.models.user_company import UserCompany
from app.schemas.entreprise_tiers import (
    EntrepriseTiersCreate,
    EntrepriseTiersUpdate,
    EntrepriseTiersResponse,
    EntrepriseTiersList,
    EntrepriseTiersStats
)
from app.crud.entreprise_tiers import EntrepriseTiersCRUD

router = APIRouter()


def get_user_company_id(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)) -> int:
    """Récupérer l'ID de l'entreprise de l'utilisateur connecté"""
    if current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Les super administrateurs ne peuvent pas accéder aux entreprises tierces"
        )
    
    user_company = db.query(UserCompany).filter(UserCompany.user_id == current_user.id).first()
    if not user_company:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Utilisateur non associé à une entreprise"
        )
    
    return user_company.company_id


@router.get("/", response_model=EntrepriseTiersList)
def get_entreprises_tiers(
    skip: int = Query(0, ge=0, description="Nombre d'éléments à ignorer"),
    limit: int = Query(50, ge=1, le=100, description="Nombre d'éléments à retourner"),
    search: Optional[str] = Query(None, description="Recherche dans nom, email, SIRET, ville"),
    is_active: Optional[bool] = Query(None, description="Filtrer par statut actif/inactif"),
    activite: Optional[str] = Query(None, description="Filtrer par activité"),
    company_id: int = Depends(get_user_company_id),
    db: Session = Depends(get_db)
):
    """Récupérer la liste des entreprises tierces avec pagination et filtres"""
    
    entreprises, total = EntrepriseTiersCRUD.get_multi(
        db=db,
        company_id=company_id,
        skip=skip,
        limit=limit,
        search=search,
        is_active=is_active,
        activite=activite
    )
    
    # Enrichir avec les informations du représentant légal
    items = []
    for entreprise in entreprises:
        item_data = EntrepriseTiersResponse.from_orm(entreprise)
        if entreprise.representant_legal:
            item_data.representant_legal_nom = f"{entreprise.representant_legal.first_name} {entreprise.representant_legal.last_name}"
            item_data.representant_legal_email = entreprise.representant_legal.email
        items.append(item_data)
    
    pages = (total + limit - 1) // limit
    
    return EntrepriseTiersList(
        items=items,
        total=total,
        page=(skip // limit) + 1,
        size=limit,
        pages=pages
    )


@router.get("/stats", response_model=EntrepriseTiersStats)
def get_entreprises_tiers_stats(
    company_id: int = Depends(get_user_company_id),
    db: Session = Depends(get_db)
):
    """Récupérer les statistiques des entreprises tierces"""
    
    stats = EntrepriseTiersCRUD.get_stats(db=db, company_id=company_id)
    return EntrepriseTiersStats(**stats)


@router.get("/{entreprise_id}", response_model=EntrepriseTiersResponse)
def get_entreprise_tiers(
    entreprise_id: int,
    company_id: int = Depends(get_user_company_id),
    db: Session = Depends(get_db)
):
    """Récupérer une entreprise tierce par son ID"""
    
    entreprise = EntrepriseTiersCRUD.get_by_id(db=db, entreprise_id=entreprise_id, company_id=company_id)
    if not entreprise:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entreprise tierce non trouvée"
        )
    
    response = EntrepriseTiersResponse.from_orm(entreprise)
    if entreprise.representant_legal:
        response.representant_legal_nom = f"{entreprise.representant_legal.first_name} {entreprise.representant_legal.last_name}"
        response.representant_legal_email = entreprise.representant_legal.email
    
    return response


@router.post("/", response_model=EntrepriseTiersResponse, status_code=status.HTTP_201_CREATED)
def create_entreprise_tiers(
    entreprise_data: EntrepriseTiersCreate,
    company_id: int = Depends(get_user_company_id),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Créer une nouvelle entreprise tierce"""
    
    try:
        entreprise = EntrepriseTiersCRUD.create(
            db=db,
            entreprise_data=entreprise_data,
            company_id=company_id,
            created_by=current_user.id
        )
        
        response = EntrepriseTiersResponse.from_orm(entreprise)
        return response
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/{entreprise_id}", response_model=EntrepriseTiersResponse)
def update_entreprise_tiers(
    entreprise_id: int,
    entreprise_data: EntrepriseTiersUpdate,
    company_id: int = Depends(get_user_company_id),
    db: Session = Depends(get_db)
):
    """Mettre à jour une entreprise tierce"""
    
    try:
        entreprise = EntrepriseTiersCRUD.update(
            db=db,
            entreprise_id=entreprise_id,
            company_id=company_id,
            entreprise_data=entreprise_data
        )
        
        if not entreprise:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Entreprise tierce non trouvée"
            )
        
        response = EntrepriseTiersResponse.from_orm(entreprise)
        if entreprise.representant_legal:
            response.representant_legal_nom = f"{entreprise.representant_legal.first_name} {entreprise.representant_legal.last_name}"
            response.representant_legal_email = entreprise.representant_legal.email
        
        return response
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{entreprise_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_entreprise_tiers(
    entreprise_id: int,
    company_id: int = Depends(get_user_company_id),
    db: Session = Depends(get_db)
):
    """Supprimer une entreprise tierce (soft delete)"""
    
    success = EntrepriseTiersCRUD.delete(db=db, entreprise_id=entreprise_id, company_id=company_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entreprise tierce non trouvée"
        )
    
    return None
